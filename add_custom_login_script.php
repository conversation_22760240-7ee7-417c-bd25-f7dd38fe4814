<?php
/**
 * إضافة كود JavaScript مخصص لتحويل تسجيل الدخول إلى التسجيل
 * 
 * طريقة الاستخدام:
 * 1. أضف هذا الكود في ملف functions.php الخاص بالثيم
 * 2. أو أنشئ plugin بسيط وأضف هذا الكود
 * 3. أو استخدم plugin لإضافة كود PHP مخصص
 */

// إضافة الكود في footer الموقع
add_action('wp_footer', 'add_custom_login_redirect_script');
add_action('login_footer', 'add_custom_login_redirect_script');

function add_custom_login_redirect_script() {
    // تأكد من أن Digits plugin مفعل
    if (!function_exists('dig_isWhatsAppEnabled')) {
        return;
    }
    
    ?>
    <script type="text/javascript">
    jQuery(document).ready(function($) {
        console.log('🚀 Custom Login Redirect Script Loaded!');
        
        // Function to switch from login to registration form
        function switchToRegistrationForm(phoneNumber, countryCode) {
            console.log('📱 Switching to registration form with:', phoneNumber, countryCode);
            
            var modal_container = $('.dig_lrf_box, .dig-box, .digits_modal_box').first();
            
            if (modal_container.length) {
                // Hide login form and show registration form
                modal_container.find('.digloginpage').hide();
                modal_container.find('.register').show();
                
                // Trigger the show register button if it exists
                var showRegisterBtn = modal_container.find('.show_register, .signupbutton');
                if (showRegisterBtn.length) {
                    showRegisterBtn.trigger('click');
                }
                
                // Pre-fill the phone number in registration form
                if (phoneNumber && countryCode) {
                    setTimeout(function() {
                        var registerForm = modal_container.find('.register');
                        
                        // Fill main phone field
                        registerForm.find('.dig-mobmail').val(phoneNumber);
                        registerForm.find('.registercountrycode').val(countryCode);
                        
                        // Also fill secondary country code if exists
                        registerForm.find('.registersecondcountrycode').val(countryCode);
                        
                        // Fill any other mobile fields
                        registerForm.find('input[name*="phone"], input[name*="mobile"]').val(phoneNumber);
                        registerForm.find('input[name*="countrycode"]').val(countryCode);
                        
                        // Focus on the phone field
                        registerForm.find('.dig-mobmail').trigger('focus').trigger('keyup');
                    }, 200);
                }
                
                // Update modal height
                setTimeout(function() {
                    $(window).trigger('resize');
                }, 300);
            }
        }
        
        // Override AJAX requests to intercept login verification
        var originalAjax = $.ajax;
        $.ajax = function(options) {
            // Check if this is the login verification request
            if (options.data && options.data.action === 'digits_check_mob' && options.data.login == 1) {
                console.log('🔍 Intercepting login verification request');
                
                var originalSuccess = options.success;
                options.success = function(result) {
                    console.log('📊 Login verification result:', result);
                    
                    var res = result;
                    if (typeof result === 'object') {
                        if (result.code !== undefined) {
                            res = result.code;
                        } else if (result.success === false) {
                            // Handle error cases
                            if (originalSuccess) {
                                originalSuccess.call(this, result);
                            }
                            return;
                        }
                    } else if (typeof result === 'string') {
                        res = result.trim();
                    }
                    
                    // Check if user doesn't exist (res == -11)
                    if (res == -11) {
                        console.log('❌ User does not exist, redirecting to registration');
                        
                        // Get phone number and country code
                        var phoneNumber = $('.dig_lrf_box .dig-mobmail, .digits_modal_box .dig-mobmail').val();
                        var countryCode = $('.dig_lrf_box .logincountrycode, .digits_modal_box .logincountrycode').val();
                        
                        // Show message
                        var message = 'يرجى التسجيل أولاً. سيتم تحويلك إلى نموذج التسجيل خلال ثوانٍ...';
                        if (typeof showDigNoticeMessage === 'function') {
                            showDigNoticeMessage(message);
                        } else if (typeof alert !== 'undefined') {
                            alert(message);
                        }
                        
                        // Switch to registration form after delay
                        setTimeout(function() {
                            switchToRegistrationForm(phoneNumber, countryCode);
                            
                            // Show additional message
                            setTimeout(function() {
                                var regMessage = 'يرجى إكمال بياناتك لإنشاء حساب جديد';
                                if (typeof showDigNoticeMessage === 'function') {
                                    showDigNoticeMessage(regMessage);
                                }
                            }, 500);
                        }, 2500);
                        
                        return; // Don't call original success function
                    }
                    
                    // Call original success function for other cases
                    if (originalSuccess) {
                        originalSuccess.call(this, result);
                    }
                };
            }
            
            // Set WhatsApp as default for all requests if enabled
            if (options.data && typeof dig_log_obj !== 'undefined' && dig_log_obj.whatsapp_enabled) {
                if (!options.data.whatsapp) {
                    options.data.whatsapp = 1;
                    console.log('📱 WhatsApp enabled for request');
                }
            }
            
            // Call original AJAX function
            return originalAjax.call(this, options);
        };
        
        console.log('✅ Custom Login Redirect Script Ready!');
    });
    </script>
    <?php
}

// إضافة متغير JavaScript لتفعيل WhatsApp
add_action('wp_footer', 'add_whatsapp_js_variable');
add_action('login_footer', 'add_whatsapp_js_variable');

function add_whatsapp_js_variable() {
    if (!function_exists('dig_isWhatsAppEnabled')) {
        return;
    }
    
    $whatsapp_enabled = dig_isWhatsAppEnabled() ? 'true' : 'false';
    ?>
    <script type="text/javascript">
    if (typeof dig_log_obj !== 'undefined') {
        dig_log_obj.whatsapp_enabled = <?php echo $whatsapp_enabled; ?>;
        console.log('WhatsApp enabled status:', dig_log_obj.whatsapp_enabled);
    }
    </script>
    <?php
}

/**
 * إضافة CSS مخصص لتحسين مظهر التحويل
 */
add_action('wp_head', 'add_custom_login_styles');
add_action('login_head', 'add_custom_login_styles');

function add_custom_login_styles() {
    ?>
    <style type="text/css">
    /* تحسين انتقال النماذج */
    .dig_lrf_box .digloginpage,
    .dig_lrf_box .register,
    .digits_modal_box .digloginpage,
    .digits_modal_box .register {
        transition: opacity 0.3s ease-in-out;
    }
    
    /* تحسين مظهر رسائل WhatsApp */
    .dig_notice_msg {
        background-color: #e8f5e8 !important;
        border-color: #25D366 !important;
        color: #155724 !important;
    }
    
    /* تحسين مظهر أزرار WhatsApp */
    .dig_use_whatsapp {
        background-color: #25D366 !important;
        color: white !important;
        border: none !important;
    }
    
    .dig_use_whatsapp:hover {
        background-color: #128C7E !important;
    }
    </style>
    <?php
}
?>
