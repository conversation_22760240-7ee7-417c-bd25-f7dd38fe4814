// إضافة الكود في footer الموقع
add_action('wp_footer', 'add_custom_login_redirect_script');
add_action('login_footer', 'add_custom_login_redirect_script');

function add_custom_login_redirect_script() {
    if (!function_exists('dig_isWhatsAppEnabled')) {
        return;
    }
    
    ?>
    <script type="text/javascript">
    jQuery(document).ready(function($) {
        console.log('🚀 Custom Login Redirect Script Loaded!');

        // Debug: Log when login button is clicked
        $(document).on('click', '.dig_lrf_box .loginviasms, .digits_modal_box .loginviasms', function() {
            console.log('🔘 Login button clicked!');
            var form = $(this).closest('form');
            var phone = form.find('.dig-mobmail').val();
            var country = form.find('.logincountrycode').val();
            console.log('📱 Phone:', phone, 'Country:', country);
        });
        
        // Function to switch from login to registration form
        function switchToRegistrationForm(phoneNumber, countryCode) {
            console.log('📱 Switching to registration form with:', phoneNumber, countryCode);

            // Try multiple selectors to find the modal container
            var modal_container = $('.dig_lrf_box').first();
            if (!modal_container.length) {
                modal_container = $('.dig-box').first();
            }
            if (!modal_container.length) {
                modal_container = $('.digits_modal_box').first();
            }

            console.log('📦 Modal container found:', modal_container.length > 0);

            if (modal_container.length) {
                // Hide login form
                modal_container.find('.digloginpage').hide();
                console.log('🔒 Login form hidden');

                // Show registration form
                var registerForm = modal_container.find('.register');
                registerForm.show();
                console.log('📝 Registration form shown:', registerForm.length > 0);

                // Try different ways to trigger registration
                var showRegisterBtn = modal_container.find('.show_register');
                if (showRegisterBtn.length) {
                    showRegisterBtn.trigger('click');
                    console.log('🔘 show_register button clicked');
                }

                var signupBtn = modal_container.find('.signupbutton');
                if (signupBtn.length) {
                    signupBtn.trigger('click');
                    console.log('🔘 signupbutton clicked');
                }

                // Pre-fill the phone number in registration form
                if (phoneNumber && countryCode) {
                    setTimeout(function() {
                        console.log('📝 Filling registration form...');

                        // Fill main phone field
                        var mobileField = registerForm.find('.dig-mobmail');
                        var countryField = registerForm.find('.registercountrycode');

                        if (mobileField.length) {
                            mobileField.val(phoneNumber);
                            console.log('📱 Mobile field filled:', phoneNumber);
                        }

                        if (countryField.length) {
                            countryField.val(countryCode);
                            console.log('🌍 Country field filled:', countryCode);
                        }

                        // Also fill secondary fields
                        registerForm.find('.registersecondcountrycode').val(countryCode);
                        registerForm.find('input[name*="phone"], input[name*="mobile"]').val(phoneNumber);
                        registerForm.find('input[name*="countrycode"]').val(countryCode);

                        // Trigger events to update UI
                        mobileField.trigger('keyup').trigger('change').trigger('focus');
                        countryField.trigger('keyup').trigger('change');

                        console.log('✅ Registration form filled and triggered');
                    }, 300);
                }

                // Update modal height and trigger resize
                setTimeout(function() {
                    $(window).trigger('resize');
                    console.log('📐 Window resize triggered');
                }, 500);
            } else {
                console.log('❌ No modal container found!');
                // Fallback: try to find and click signup link
                var signupLink = $('a[href*="register"], .signup, .sign-up').first();
                if (signupLink.length) {
                    signupLink.trigger('click');
                    console.log('🔗 Signup link clicked as fallback');
                }
            }
        }
        
        // Override AJAX requests to intercept login verification
        var originalAjax = $.ajax;
        $.ajax = function(options) {
            // Check if this is the login verification request
            if (options.data && options.data.action === 'digits_check_mob' && options.data.login == 1) {
                console.log('🔍 Intercepting login verification request for:', options.data.mobileNo);

                var originalSuccess = options.success;
                options.success = function(result) {
                    console.log('📊 Raw login verification result:', result);
                    console.log('📊 Result type:', typeof result);

                    var res = result;

                    // Handle different response formats
                    if (typeof result === 'string') {
                        try {
                            // Try to parse as JSON first
                            var parsed = JSON.parse(result);
                            if (parsed.code !== undefined) {
                                res = parsed.code;
                            } else if (parsed.success === false) {
                                res = parsed.data || result;
                            }
                        } catch (e) {
                            // If not JSON, use as string
                            res = result.trim();
                        }
                    } else if (typeof result === 'object') {
                        if (result.code !== undefined) {
                            res = result.code;
                        } else if (result.success === false) {
                            res = result.data || -11; // Assume user doesn't exist
                        }
                    }

                    console.log('📊 Processed result:', res);

                    // Check if user doesn't exist (res == -11 or specific error messages)
                    if (res == -11 || res == '-11' ||
                        (typeof res === 'string' && (res.includes('not found') || res.includes('signup')))) {

                        console.log('❌ User does not exist, redirecting to registration');

                        // Get phone number and country code
                        var phoneNumber = $('.dig_lrf_box .dig-mobmail, .digits_modal_box .dig-mobmail').val();
                        var countryCode = $('.dig_lrf_box .logincountrycode, .digits_modal_box .logincountrycode').val();

                        console.log('📱 Phone:', phoneNumber, 'Country:', countryCode);

                        // Show message
                        var message = 'يرجى التسجيل أولاً. سيتم تحويلك إلى نموذج التسجيل خلال ثوانٍ...';
                        if (typeof showDigNoticeMessage === 'function') {
                            showDigNoticeMessage(message);
                        } else {
                            alert(message);
                        }

                        // Switch to registration form after delay
                        setTimeout(function() {
                            console.log('🔄 Starting switch to registration form...');
                            switchToRegistrationForm(phoneNumber, countryCode);

                            // Show additional message
                            setTimeout(function() {
                                var regMessage = 'يرجى إكمال بياناتك لإنشاء حساب جديد';
                                if (typeof showDigNoticeMessage === 'function') {
                                    showDigNoticeMessage(regMessage);
                                }
                            }, 500);
                        }, 2500);

                        return; // Don't call original success function
                    }

                    // Call original success function for other cases
                    if (originalSuccess) {
                        originalSuccess.call(this, result);
                    }
                };
            }

            // Set WhatsApp as default for all requests if enabled
            if (options.data && typeof dig_log_obj !== 'undefined' && dig_log_obj.whatsapp_enabled) {
                if (!options.data.whatsapp) {
                    options.data.whatsapp = 1;
                    console.log('📱 WhatsApp enabled for request');
                }
            }

            // Call original AJAX function
            return originalAjax.call(this, options);
        };
        
        console.log('✅ Custom Login Redirect Script Ready!');
    });
    </script>
    <?php
}

// إضافة متغير JavaScript لتفعيل WhatsApp
add_action('wp_footer', 'add_whatsapp_js_variable');
add_action('login_footer', 'add_whatsapp_js_variable');

function add_whatsapp_js_variable() {
    if (!function_exists('dig_isWhatsAppEnabled')) {
        return;
    }
    
    $whatsapp_enabled = dig_isWhatsAppEnabled() ? 'true' : 'false';
    ?>
    <script type="text/javascript">
    if (typeof dig_log_obj !== 'undefined') {
        dig_log_obj.whatsapp_enabled = <?php echo $whatsapp_enabled; ?>;
        console.log('WhatsApp enabled status:', dig_log_obj.whatsapp_enabled);
    }
    </script>
    <?php
}

/**
 * إضافة CSS مخصص لتحسين مظهر التحويل
 */
add_action('wp_head', 'add_custom_login_styles');
add_action('login_head', 'add_custom_login_styles');

function add_custom_login_styles() {
    ?>
    <style type="text/css">
    /* تحسين انتقال النماذج */
    .dig_lrf_box .digloginpage,
    .dig_lrf_box .register,
    .digits_modal_box .digloginpage,
    .digits_modal_box .register {
        transition: opacity 0.3s ease-in-out;
    }
    
    /* تحسين مظهر رسائل WhatsApp */
    .dig_notice_msg {
        background-color: #e8f5e8 !important;
        border-color: #25D366 !important;
        color: #155724 !important;
    }
    
    /* تحسين مظهر أزرار WhatsApp */
    .dig_use_whatsapp {
        background-color: #25D366 !important;
        color: white !important;
        border: none !important;
    }
    
    .dig_use_whatsapp:hover {
        background-color: #128C7E !important;
    }
    </style>
    <?php
}
