<?php
/**
 * حل بسيط ومباشر للتحويل التلقائي من تسجيل الدخول إلى التسجيل
 * أضف هذا الكود في functions.php أو كـ plugin منفصل
 */

// إضافة الكود في footer
add_action('wp_footer', 'simple_login_redirect_script', 999);
add_action('login_footer', 'simple_login_redirect_script', 999);

function simple_login_redirect_script() {
    // تأكد من وجود Digits
    if (!function_exists('dig_isWhatsAppEnabled')) {
        return;
    }
    ?>
    <script type="text/javascript">
    jQuery(document).ready(function($) {
        console.log('🚀 Simple Login Redirect Loaded!');
        
        // مراقبة رسائل الخطأ التي تظهر
        function watchForSignupMessages() {
            // مراقبة التغييرات في DOM
            var observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) {
                            var $node = $(node);
                            var text = $node.text().toLowerCase();
                            
                            // البحث عن رسائل تطلب التسجيل
                            if (text.includes('signup') || 
                                text.includes('register') || 
                                text.includes('التسجيل') ||
                                text.includes('please signup') ||
                                text.includes('يرجى التسجيل')) {
                                
                                console.log('📢 Signup message detected:', text);
                                
                                // الحصول على رقم الهاتف
                                var phoneNumber = $('.dig-mobmail').val() || $('.mobile_field').val();
                                var countryCode = $('.logincountrycode').val() || $('.countrycode').val();
                                
                                console.log('📱 Phone data:', phoneNumber, countryCode);
                                
                                // إخفاء الرسالة الأصلية
                                $node.hide();
                                
                                // إظهار رسالة مخصصة
                                alert('سيتم تحويلك إلى نموذج التسجيل خلال ثوانٍ...');
                                
                                // التحويل بعد تأخير
                                setTimeout(function() {
                                    redirectToRegistration(phoneNumber, countryCode);
                                }, 2000);
                            }
                        }
                    });
                });
            });
            
            // بدء المراقبة
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            console.log('👁️ Message observer started');
        }
        
        // دالة التحويل إلى التسجيل
        function redirectToRegistration(phoneNumber, countryCode) {
            console.log('🔄 Redirecting to registration...');
            
            // البحث عن النموذج
            var loginForm = $('.digloginpage').first();
            var registerForm = $('.register').first();
            
            console.log('📋 Forms found - Login:', loginForm.length, 'Register:', registerForm.length);
            
            if (loginForm.length && registerForm.length) {
                // إخفاء نموذج تسجيل الدخول
                loginForm.hide();
                
                // إظهار نموذج التسجيل
                registerForm.show();
                
                // محاولة تفعيل زر التسجيل
                var signupBtn = $('.signupbutton, .show_register').first();
                if (signupBtn.length) {
                    signupBtn.trigger('click');
                    console.log('🔘 Signup button triggered');
                }
                
                // ملء البيانات
                if (phoneNumber && countryCode) {
                    setTimeout(function() {
                        var regMobile = registerForm.find('.dig-mobmail');
                        var regCountry = registerForm.find('.registercountrycode');
                        
                        if (regMobile.length) {
                            regMobile.val(phoneNumber).trigger('keyup');
                            console.log('📱 Mobile filled:', phoneNumber);
                        }
                        
                        if (regCountry.length) {
                            regCountry.val(countryCode).trigger('keyup');
                            console.log('🌍 Country filled:', countryCode);
                        }
                        
                        // التركيز على الحقل
                        regMobile.focus();
                        
                        console.log('✅ Registration form ready!');
                    }, 500);
                }
            } else {
                console.log('❌ Forms not found, trying alternative method...');
                
                // طريقة بديلة: البحث عن رابط التسجيل
                var signupLink = $('a[href*="register"], .signup-link, [data-action="register"]').first();
                if (signupLink.length) {
                    signupLink.trigger('click');
                    console.log('🔗 Signup link clicked');
                } else {
                    // طريقة أخيرة: إعادة توجيه الصفحة
                    var currentUrl = window.location.href;
                    if (currentUrl.includes('login')) {
                        window.location.href = currentUrl.replace('login', 'register');
                    } else {
                        window.location.href = currentUrl + (currentUrl.includes('?') ? '&' : '?') + 'action=register';
                    }
                }
            }
        }
        
        // بدء المراقبة
        watchForSignupMessages();
        
        // أيضاً مراقبة النقر على زر تسجيل الدخول
        $(document).on('click', '.loginviasms, .dig_login_submit', function() {
            console.log('🔘 Login button clicked, starting enhanced monitoring...');
            
            // مراقبة مكثفة لمدة 10 ثوان
            var intensiveWatch = setInterval(function() {
                var errorMessages = $('.dig_notice_msg, .error, .notice, .alert').filter(':visible');
                
                errorMessages.each(function() {
                    var text = $(this).text().toLowerCase();
                    if (text.includes('signup') || text.includes('register') || text.includes('التسجيل')) {
                        console.log('📢 Error message found:', text);
                        
                        var phoneNumber = $('.dig-mobmail').val();
                        var countryCode = $('.logincountrycode').val();
                        
                        $(this).hide();
                        alert('سيتم تحويلك إلى نموذج التسجيل...');
                        
                        setTimeout(function() {
                            redirectToRegistration(phoneNumber, countryCode);
                        }, 1000);
                        
                        clearInterval(intensiveWatch);
                    }
                });
            }, 500);
            
            // إيقاف المراقبة المكثفة بعد 10 ثوان
            setTimeout(function() {
                clearInterval(intensiveWatch);
            }, 10000);
        });
        
        console.log('✅ Simple Login Redirect Ready!');
    });
    </script>
    <?php
}

// إضافة CSS للتحسينات البصرية
add_action('wp_head', 'simple_login_redirect_styles');
add_action('login_head', 'simple_login_redirect_styles');

function simple_login_redirect_styles() {
    ?>
    <style type="text/css">
    /* تحسين انتقال النماذج */
    .digloginpage, .register {
        transition: all 0.3s ease-in-out;
    }
    
    /* إخفاء رسائل الخطأ الأصلية مؤقتاً */
    .dig_notice_msg:contains("signup"),
    .dig_notice_msg:contains("register"),
    .dig_notice_msg:contains("التسجيل") {
        display: none !important;
    }
    </style>
    <?php
}
?>
