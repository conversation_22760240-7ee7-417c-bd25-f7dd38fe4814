// كود للاختبار المباشر في Console
// انسخ هذا الكود والصقه في Console لاختبار التحويل التلقائي

console.log('🧪 Starting Console Test for Login Redirect...');

// Function to switch to registration form
function testSwitchToRegistration(phoneNumber, countryCode) {
    console.log('🔄 Testing switch to registration with:', phoneNumber, countryCode);
    
    // Find modal container
    var modal_container = jQuery('.dig_lrf_box').first();
    if (!modal_container.length) {
        modal_container = jQuery('.dig-box').first();
    }
    if (!modal_container.length) {
        modal_container = jQuery('.digits_modal_box').first();
    }
    
    console.log('📦 Modal container found:', modal_container.length > 0);
    
    if (modal_container.length) {
        // Hide login form
        modal_container.find('.digloginpage').hide();
        console.log('🔒 Login form hidden');
        
        // Show registration form
        var registerForm = modal_container.find('.register');
        registerForm.show();
        console.log('📝 Registration form shown:', registerForm.length > 0);
        
        // Try to trigger registration
        var signupBtn = modal_container.find('.signupbutton');
        if (signupBtn.length) {
            signupBtn.trigger('click');
            console.log('🔘 Signup button clicked');
        }
        
        // Fill form
        setTimeout(function() {
            var mobileField = registerForm.find('.dig-mobmail');
            var countryField = registerForm.find('.registercountrycode');
            
            if (mobileField.length && phoneNumber) {
                mobileField.val(phoneNumber);
                mobileField.trigger('keyup');
                console.log('📱 Mobile field filled:', phoneNumber);
            }
            
            if (countryField.length && countryCode) {
                countryField.val(countryCode);
                countryField.trigger('keyup');
                console.log('🌍 Country field filled:', countryCode);
            }
        }, 500);
    } else {
        console.log('❌ No modal container found!');
    }
}

// Override AJAX for testing
var originalAjax = jQuery.ajax;
jQuery.ajax = function(options) {
    if (options.data && options.data.action === 'digits_check_mob' && options.data.login == 1) {
        console.log('🔍 INTERCEPTED LOGIN REQUEST:', options.data);
        
        var originalSuccess = options.success;
        options.success = function(result) {
            console.log('📊 LOGIN RESPONSE:', result);
            console.log('📊 Response type:', typeof result);
            
            // Force test the redirect (uncomment next line to test)
            // result = -11;
            
            var res = result;
            if (typeof result === 'string') {
                try {
                    var parsed = JSON.parse(result);
                    res = parsed.code || parsed;
                } catch (e) {
                    res = result.trim();
                }
            } else if (typeof result === 'object' && result.code !== undefined) {
                res = result.code;
            }
            
            console.log('📊 Processed response:', res);
            
            if (res == -11 || res == '-11') {
                console.log('❌ USER NOT FOUND - REDIRECTING TO REGISTRATION');
                
                var phoneNumber = jQuery('.dig_lrf_box .dig-mobmail, .digits_modal_box .dig-mobmail').val();
                var countryCode = jQuery('.dig_lrf_box .logincountrycode, .digits_modal_box .logincountrycode').val();
                
                alert('يرجى التسجيل أولاً. سيتم تحويلك إلى نموذج التسجيل...');
                
                setTimeout(function() {
                    testSwitchToRegistration(phoneNumber, countryCode);
                }, 2000);
                
                return; // Don't call original success
            }
            
            // Call original success for other cases
            if (originalSuccess) {
                originalSuccess.call(this, result);
            }
        };
    }
    
    return originalAjax.call(this, options);
};

console.log('✅ Console test setup complete!');
console.log('📝 Now try to login with a non-existing phone number');
console.log('🔧 To force test the redirect, uncomment line 67 in this code');

// Test function you can call manually
window.testLoginRedirect = function(phone, country) {
    phone = phone || '1234567890';
    country = country || '+1';
    console.log('🧪 Manual test with phone:', phone, 'country:', country);
    testSwitchToRegistration(phone, country);
};

console.log('🎯 You can also call: testLoginRedirect("1234567890", "+1")');
