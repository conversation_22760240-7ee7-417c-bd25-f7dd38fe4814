<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تشخيص المشكلة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .debug-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .step {
            background: #e3f2fd;
            border-left: 4px solid #2196F3;
            padding: 15px;
            margin: 15px 0;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            direction: ltr;
            text-align: left;
        }
        .warning {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص مشكلة عدم عمل التحديثات</h1>
        
        <div class="debug-section">
            <h3>⚠️ المشكلة المحتملة</h3>
            <p>إذا لم تعمل التحديثات، فقد تكون المشكلة في أحد الأسباب التالية:</p>
            <ul>
                <li><strong>Cache WordPress:</strong> الموقع يستخدم cache</li>
                <li><strong>Plugin Cache:</strong> هناك plugin للـ cache يمنع تحديث الملفات</li>
                <li><strong>Browser Cache:</strong> المتصفح يحفظ النسخة القديمة</li>
                <li><strong>CDN:</strong> إذا كان الموقع يستخدم CDN</li>
                <li><strong>ملف مختلف:</strong> WordPress يحمل ملف من مكان آخر</li>
            </ul>
        </div>

        <div class="step">
            <h3>🧪 خطوة 1: اختبار تحميل الملف</h3>
            <p>أضفت كود تجريبي في بداية دالة تسجيل الدخول:</p>
            <div class="code-block">
console.log('LOGIN BUTTON CLICKED - NEW CODE IS WORKING!');
            </div>
            <p><strong>كيفية الاختبار:</strong></p>
            <ol>
                <li>افتح صفحة تسجيل الدخول</li>
                <li>اضغط F12 لفتح Developer Tools</li>
                <li>اذهب لتبويب Console</li>
                <li>اضغط على زر "Continue" في تسجيل الدخول</li>
                <li>إذا ظهرت الرسالة في Console، فالملف يتم تحميله</li>
            </ol>
        </div>

        <div class="step">
            <h3>🧹 خطوة 2: مسح الـ Cache</h3>
            <p>جرب هذه الخطوات بالترتيب:</p>
            <ol>
                <li><strong>مسح cache المتصفح:</strong> Ctrl+Shift+R أو Ctrl+F5</li>
                <li><strong>مسح cache WordPress:</strong> إذا كان لديك plugin للـ cache</li>
                <li><strong>تعطيل cache مؤقتاً:</strong> لاختبار التحديثات</li>
                <li><strong>تجربة متصفح آخر:</strong> أو وضع التصفح الخفي</li>
            </ol>
        </div>

        <div class="step">
            <h3>🔧 خطوة 3: التحقق من تحميل الملف الصحيح</h3>
            <p>تأكد من أن WordPress يحمل الملف الصحيح:</p>
            <ol>
                <li>افتح Developer Tools (F12)</li>
                <li>اذهب لتبويب Network</li>
                <li>أعد تحميل الصفحة</li>
                <li>ابحث عن ملف <code>login.js</code></li>
                <li>تأكد من أنه يحمل من المسار الصحيح</li>
            </ol>
        </div>

        <div class="warning">
            <h3>🚨 إذا لم تظهر الرسالة في Console</h3>
            <p>هذا يعني أن WordPress لا يحمل الملف المحدث. جرب:</p>
            <ul>
                <li>تأكد من حفظ الملف بشكل صحيح</li>
                <li>تحقق من صلاحيات الملف</li>
                <li>جرب رفع الملف مرة أخرى عبر FTP</li>
                <li>تأكد من أن المسار صحيح في WordPress</li>
            </ul>
        </div>

        <div class="step">
            <h3>🔄 خطوة 4: حل بديل - إضافة الكود مباشرة</h3>
            <p>إذا لم تعمل الطرق السابقة، يمكن إضافة الكود مباشرة في footer:</p>
            <div class="code-block">
// أضف هذا الكود في footer.php أو عبر WordPress admin
&lt;script&gt;
jQuery(document).ready(function($) {
    // Override the original function
    $(document).off('click', '.dig_lrf_box .loginviasms');
    
    $(document).on('click', '.dig_lrf_box .loginviasms', function() {
        console.log('CUSTOM LOGIN CODE RUNNING!');
        
        // Your custom login logic here
        var phoneNumber = $(this).closest('form').find('.dig-mobmail').val();
        var countryCode = $(this).closest('form').find('.logincountrycode').val();
        
        // Check if user exists logic...
        // If not exists, redirect to registration
    });
});
&lt;/script&gt;
            </div>
        </div>

        <div class="debug-section">
            <h3>📞 تواصل للمساعدة</h3>
            <p>إذا استمرت المشكلة، يرجى إرسال:</p>
            <ul>
                <li>لقطة شاشة من Console في Developer Tools</li>
                <li>لقطة شاشة من تبويب Network</li>
                <li>معلومات عن plugins الـ cache المستخدمة</li>
                <li>نوع الاستضافة المستخدمة</li>
            </ul>
        </div>
    </div>
</body>
</html>
