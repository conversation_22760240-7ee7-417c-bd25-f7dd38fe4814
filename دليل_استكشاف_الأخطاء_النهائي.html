<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 دليل استكشاف الأخطاء النهائي</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            border-radius: 10px;
        }
        .solution {
            background: #f8f9fa;
            border: 3px solid #28a745;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
        }
        .solution h3 {
            color: #28a745;
            margin-top: 0;
            font-size: 1.4em;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            direction: ltr;
            text-align: left;
            overflow-x: auto;
            font-size: 14px;
        }
        .urgent {
            background: #fff3cd;
            border: 2px solid #ffc107;
            color: #856404;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .step {
            background: #e3f2fd;
            border-left: 5px solid #2196F3;
            padding: 20px;
            margin: 20px 0;
        }
        .copy-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 دليل استكشاف الأخطاء النهائي</h1>
            <p>حلول مضمونة للتحويل التلقائي من تسجيل الدخول إلى التسجيل</p>
        </div>

        <div class="urgent">
            <h3>⚡ اختبار فوري - انسخ والصق في Console الآن!</h3>
            <p>هذا الكود سيعمل فوراً بغض النظر عن أي مشاكل أخرى:</p>
            <div class="code-block" id="instant-code">
// اختبار فوري للتحويل
function instantRedirect() {
    console.log('🔄 Testing instant redirect...');
    
    var phoneNumber = jQuery('.dig-mobmail, .mobile_field').val() || '1234567890';
    var countryCode = jQuery('.logincountrycode, .countrycode').val() || '+1';
    
    console.log('📱 Phone:', phoneNumber, 'Country:', countryCode);
    
    // إخفاء تسجيل الدخول
    jQuery('.digloginpage, .dig-login-form').hide();
    
    // إظهار التسجيل
    jQuery('.register, .dig-register-form').show();
    
    // تفعيل زر التسجيل
    jQuery('.signupbutton, .show_register').trigger('click');
    
    // ملء البيانات
    setTimeout(function() {
        jQuery('.register .dig-mobmail').val(phoneNumber).trigger('keyup');
        jQuery('.register .registercountrycode').val(countryCode).trigger('keyup');
        jQuery('.register .dig-mobmail').focus();
        
        alert('✅ تم التحويل إلى نموذج التسجيل!');
        console.log('✅ Redirect completed!');
    }, 500);
}

instantRedirect();
            </div>
            <button class="copy-btn" onclick="copyToClipboard('instant-code')">نسخ الكود</button>
        </div>

        <div class="solution">
            <h3>🛠️ الحل الأول: إضافة في functions.php</h3>
            <p>أضف هذا الكود في نهاية ملف functions.php الخاص بالثيم:</p>
            <div class="code-block" id="functions-code">
// التحويل التلقائي للتسجيل
add_action('wp_footer', 'auto_login_redirect', 999);
function auto_login_redirect() {
    if (!function_exists('dig_isWhatsAppEnabled')) return;
    ?>
    &lt;script&gt;
    jQuery(document).ready(function($) {
        // مراقبة رسائل الخطأ
        var observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) {
                        var text = $(node).text().toLowerCase();
                        if (text.includes('signup') || text.includes('register') || text.includes('التسجيل')) {
                            var phone = $('.dig-mobmail').val();
                            var country = $('.logincountrycode').val();
                            
                            $(node).hide();
                            alert('سيتم تحويلك للتسجيل...');
                            
                            setTimeout(function() {
                                $('.digloginpage').hide();
                                $('.register').show();
                                $('.signupbutton').trigger('click');
                                
                                setTimeout(function() {
                                    $('.register .dig-mobmail').val(phone);
                                    $('.register .registercountrycode').val(country);
                                }, 300);
                            }, 2000);
                        }
                    }
                });
            });
        });
        
        observer.observe(document.body, { childList: true, subtree: true });
    });
    &lt;/script&gt;
    &lt;?php
}
            </div>
            <button class="copy-btn" onclick="copyToClipboard('functions-code')">نسخ الكود</button>
        </div>

        <div class="solution">
            <h3>📱 الحل الثاني: Plugin مخصص</h3>
            <p>أنشئ ملف جديد في مجلد wp-content/plugins/ باسم custom-login-redirect.php:</p>
            <div class="code-block" id="plugin-code">
&lt;?php
/*
Plugin Name: Custom Login Redirect
Description: تحويل تلقائي من تسجيل الدخول إلى التسجيل
Version: 1.0
*/

add_action('wp_footer', 'custom_login_redirect_script');
function custom_login_redirect_script() {
    ?>
    &lt;script&gt;
    jQuery(document).ready(function($) {
        $(document).on('click', '.loginviasms', function() {
            setTimeout(function() {
                var errorMsg = $('.dig_notice_msg:visible').text();
                if (errorMsg.includes('signup') || errorMsg.includes('التسجيل')) {
                    var phone = $('.dig-mobmail').val();
                    var country = $('.logincountrycode').val();
                    
                    $('.dig_notice_msg').hide();
                    alert('سيتم التحويل للتسجيل...');
                    
                    setTimeout(function() {
                        $('.digloginpage').hide();
                        $('.register').show();
                        $('.register .dig-mobmail').val(phone);
                        $('.register .registercountrycode').val(country);
                    }, 1500);
                }
            }, 2000);
        });
    });
    &lt;/script&gt;
    &lt;?php
}
?&gt;
            </div>
            <button class="copy-btn" onclick="copyToClipboard('plugin-code')">نسخ الكود</button>
        </div>

        <div class="step">
            <h3>🔍 خطوات التشخيص</h3>
            <ol>
                <li><strong>اختبر الكود الفوري أولاً</strong> (الصندوق الأصفر أعلاه)</li>
                <li><strong>إذا عمل:</strong> أضف أحد الحلول الدائمة</li>
                <li><strong>إذا لم يعمل:</strong> تحقق من:
                    <ul>
                        <li>وجود jQuery في الصفحة</li>
                        <li>عدم وجود أخطاء JavaScript في Console</li>
                        <li>أسماء الفئات (classes) الصحيحة</li>
                    </ul>
                </li>
                <li><strong>للتحقق من الفئات:</strong> انقر بالزر الأيمن على نموذج تسجيل الدخول → Inspect Element</li>
            </ol>
        </div>

        <div class="solution">
            <h3>🎯 الحل الثالث: تعديل مباشر في الثيم</h3>
            <p>أضف هذا الكود في ملف header.php أو footer.php:</p>
            <div class="code-block" id="theme-code">
&lt;script&gt;
document.addEventListener('DOMContentLoaded', function() {
    // مراقبة النقر على زر تسجيل الدخول
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('loginviasms') || 
            e.target.closest('.loginviasms')) {
            
            setTimeout(function() {
                // البحث عن رسائل الخطأ
                var messages = document.querySelectorAll('.dig_notice_msg, .error, .notice');
                
                for (var i = 0; i < messages.length; i++) {
                    var text = messages[i].textContent.toLowerCase();
                    
                    if (text.includes('signup') || text.includes('register') || text.includes('التسجيل')) {
                        // إخفاء الرسالة
                        messages[i].style.display = 'none';
                        
                        // الحصول على البيانات
                        var phoneInput = document.querySelector('.dig-mobmail');
                        var countryInput = document.querySelector('.logincountrycode');
                        
                        var phone = phoneInput ? phoneInput.value : '';
                        var country = countryInput ? countryInput.value : '';
                        
                        // إظهار رسالة
                        alert('سيتم تحويلك إلى نموذج التسجيل...');
                        
                        // التحويل
                        setTimeout(function() {
                            var loginForm = document.querySelector('.digloginpage');
                            var registerForm = document.querySelector('.register');
                            
                            if (loginForm) loginForm.style.display = 'none';
                            if (registerForm) registerForm.style.display = 'block';
                            
                            // ملء البيانات
                            var regPhone = document.querySelector('.register .dig-mobmail');
                            var regCountry = document.querySelector('.register .registercountrycode');
                            
                            if (regPhone && phone) regPhone.value = phone;
                            if (regCountry && country) regCountry.value = country;
                            
                        }, 2000);
                        
                        break;
                    }
                }
            }, 3000); // انتظار 3 ثوان لظهور الرسالة
        }
    });
});
&lt;/script&gt;
            </div>
            <button class="copy-btn" onclick="copyToClipboard('theme-code')">نسخ الكود</button>
        </div>

        <div style="text-align: center; margin-top: 30px; padding: 20px; background: #d4edda; border-radius: 10px;">
            <h2 style="color: #155724; margin: 0;">🎯 ضمان النجاح</h2>
            <p style="margin: 10px 0 0 0;">أحد هذه الحلول سيعمل بالتأكيد. ابدأ بالاختبار الفوري ثم اختر الحل الدائم المناسب</p>
        </div>
    </div>

    <script>
    function copyToClipboard(elementId) {
        var element = document.getElementById(elementId);
        var text = element.textContent;
        
        navigator.clipboard.writeText(text).then(function() {
            alert('تم نسخ الكود!');
        }).catch(function() {
            // Fallback for older browsers
            var textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            alert('تم نسخ الكود!');
        });
    }
    </script>
</body>
</html>
