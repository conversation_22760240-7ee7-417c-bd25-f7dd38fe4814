// كود للاختبار الفوري - انسخ والصق في Console
console.log('🧪 Starting Instant Test...');

// دالة بسيطة للتحويل
function instantRedirect() {
    console.log('🔄 Testing instant redirect...');
    
    // الحصول على البيانات
    var phoneNumber = jQuery('.dig-mobmail, .mobile_field').val() || '1234567890';
    var countryCode = jQuery('.logincountrycode, .countrycode').val() || '+1';
    
    console.log('📱 Using phone:', phoneNumber, 'country:', countryCode);
    
    // البحث عن النماذج
    var loginForm = jQuery('.digloginpage, .dig-login-form').first();
    var registerForm = jQuery('.register, .dig-register-form').first();
    
    console.log('📋 Login form found:', loginForm.length > 0);
    console.log('📋 Register form found:', registerForm.length > 0);
    
    if (loginForm.length && registerForm.length) {
        // إخفاء تسجيل الدخول
        loginForm.hide();
        console.log('🔒 Login form hidden');
        
        // إظهار التسجيل
        registerForm.show();
        console.log('📝 Register form shown');
        
        // تفعيل زر التسجيل
        var signupBtn = jQuery('.signupbutton, .show_register, [data-action="register"]');
        if (signupBtn.length) {
            signupBtn.first().trigger('click');
            console.log('🔘 Signup button clicked');
        }
        
        // ملء البيانات
        setTimeout(function() {
            var mobileField = registerForm.find('.dig-mobmail, .mobile_field');
            var countryField = registerForm.find('.registercountrycode, .countrycode');
            
            if (mobileField.length) {
                mobileField.val(phoneNumber).trigger('keyup').trigger('change');
                console.log('📱 Mobile field filled');
            }
            
            if (countryField.length) {
                countryField.val(countryCode).trigger('keyup').trigger('change');
                console.log('🌍 Country field filled');
            }
            
            // التركيز
            mobileField.focus();
            
            console.log('✅ Instant redirect completed!');
            alert('تم التحويل إلى نموذج التسجيل بنجاح!');
        }, 500);
        
    } else {
        console.log('❌ Forms not found, trying alternative...');
        
        // طريقة بديلة: البحث عن أي عنصر يحتوي على "register"
        var registerElements = jQuery('[class*="register"], [id*="register"], a[href*="register"]');
        console.log('🔍 Found register elements:', registerElements.length);
        
        if (registerElements.length) {
            registerElements.first().trigger('click');
            console.log('🔗 Register element clicked');
        } else {
            // طريقة أخيرة: تغيير URL
            var newUrl = window.location.href;
            if (newUrl.includes('login')) {
                newUrl = newUrl.replace('login', 'register');
            } else {
                newUrl += (newUrl.includes('?') ? '&' : '?') + 'action=register';
            }
            
            console.log('🌐 Redirecting to:', newUrl);
            window.location.href = newUrl;
        }
    }
}

// تشغيل الاختبار
instantRedirect();

// إضافة دالة للاختبار اليدوي
window.testLoginRedirect = instantRedirect;

console.log('✅ Instant test completed!');
console.log('🔧 You can run it again with: testLoginRedirect()');

// مراقبة رسائل الخطأ
var messageWatcher = setInterval(function() {
    var messages = jQuery('.dig_notice_msg, .error, .notice, .alert, .message').filter(':visible');
    
    messages.each(function() {
        var text = jQuery(this).text().toLowerCase();
        if (text.includes('signup') || text.includes('register') || text.includes('التسجيل')) {
            console.log('📢 Signup message detected:', text);
            jQuery(this).css('background-color', 'yellow');
            
            // تشغيل التحويل التلقائي
            setTimeout(function() {
                instantRedirect();
            }, 2000);
            
            clearInterval(messageWatcher);
        }
    });
}, 1000);

// إيقاف المراقبة بعد 30 ثانية
setTimeout(function() {
    clearInterval(messageWatcher);
    console.log('⏰ Message watcher stopped');
}, 30000);

console.log('👁️ Message watcher started for 30 seconds');
