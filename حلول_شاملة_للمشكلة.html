<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 حلول شاملة لمشكلة عدم عمل التحديثات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border-radius: 10px;
        }
        .solution {
            background: #f8f9fa;
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .solution h3 {
            color: #28a745;
            margin-top: 0;
        }
        .debug-step {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            direction: ltr;
            text-align: left;
            overflow-x: auto;
        }
        .warning {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .file-path {
            background: #e9ecef;
            padding: 5px 10px;
            border-radius: 4px;
            font-family: monospace;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 حلول شاملة لمشكلة عدم عمل التحديثات</h1>
            <p>عدة طرق لحل مشكلة عدم عمل التحويل التلقائي من تسجيل الدخول إلى التسجيل</p>
        </div>

        <div class="debug-step">
            <h3>🔍 أولاً: تشخيص المشكلة</h3>
            <p>أضفت كود تجريبي في الملفات. للتحقق من عمل التحديثات:</p>
            <ol>
                <li>افتح صفحة تسجيل الدخول</li>
                <li>اضغط F12 → Console</li>
                <li>اضغط زر "Continue"</li>
                <li>إذا ظهرت رسالة "LOGIN BUTTON CLICKED - NEW CODE IS WORKING!" فالملف محدث</li>
                <li>إذا لم تظهر، فهناك مشكلة في التحميل</li>
            </ol>
        </div>

        <div class="solution">
            <h3>✅ الحل الأول: إضافة كود مخصص في WordPress</h3>
            <p><strong>الأسهل والأضمن - يعمل بغض النظر عن مشاكل الـ cache</strong></p>
            
            <h4>الطريقة 1: عبر functions.php</h4>
            <ol>
                <li>اذهب إلى <span class="file-path">wp-content/themes/[theme-name]/functions.php</span></li>
                <li>أضف الكود التالي في نهاية الملف:</li>
            </ol>
            <div class="code-block">
// إضافة كود التحويل التلقائي للتسجيل
add_action('wp_footer', 'add_custom_login_redirect');
function add_custom_login_redirect() {
    if (!function_exists('dig_isWhatsAppEnabled')) return;
    ?>
    &lt;script&gt;
    jQuery(document).ready(function($) {
        var originalAjax = $.ajax;
        $.ajax = function(options) {
            if (options.data && options.data.action === 'digits_check_mob' && options.data.login == 1) {
                var originalSuccess = options.success;
                options.success = function(result) {
                    var res = (typeof result === 'object' && result.code !== undefined) ? result.code : result.trim();
                    if (res == -11) {
                        alert('يرجى التسجيل أولاً. سيتم تحويلك إلى نموذج التسجيل...');
                        setTimeout(function() {
                            $('.dig_lrf_box .digloginpage').hide();
                            $('.dig_lrf_box .register').show();
                            $('.dig_lrf_box .signupbutton').trigger('click');
                            var phone = $('.dig_lrf_box .dig-mobmail').val();
                            var country = $('.dig_lrf_box .logincountrycode').val();
                            setTimeout(function() {
                                $('.register .dig-mobmail').val(phone);
                                $('.register .registercountrycode').val(country);
                            }, 200);
                        }, 2000);
                        return;
                    }
                    if (originalSuccess) originalSuccess.call(this, result);
                };
            }
            return originalAjax.call(this, options);
        };
    });
    &lt;/script&gt;
    &lt;?php
}
            </div>

            <h4>الطريقة 2: عبر plugin مخصص</h4>
            <p>أنشئ ملف <span class="file-path">custom-login-redirect.php</span> في مجلد plugins:</p>
            <div class="code-block">
&lt;?php
/*
Plugin Name: Custom Login Redirect
Description: تحويل تلقائي من تسجيل الدخول إلى التسجيل
Version: 1.0
*/

// استخدم نفس الكود أعلاه هنا
            </div>
        </div>

        <div class="solution">
            <h3>🔄 الحل الثاني: مسح الـ Cache</h3>
            <p>إذا كانت التحديثات موجودة لكن لا تعمل:</p>
            
            <h4>خطوات مسح الـ Cache:</h4>
            <ol>
                <li><strong>Cache المتصفح:</strong> Ctrl+Shift+R أو Ctrl+F5</li>
                <li><strong>Cache WordPress:</strong>
                    <ul>
                        <li>WP Rocket: اذهب لإعدادات WP Rocket → Clear Cache</li>
                        <li>W3 Total Cache: Performance → Purge All Caches</li>
                        <li>WP Super Cache: Settings → Delete Cache</li>
                        <li>LiteSpeed Cache: LiteSpeed Cache → Purge All</li>
                    </ul>
                </li>
                <li><strong>CDN Cache:</strong> إذا كنت تستخدم Cloudflare أو CDN آخر</li>
                <li><strong>Server Cache:</strong> تواصل مع مزود الاستضافة</li>
            </ol>
        </div>

        <div class="solution">
            <h3>🛠️ الحل الثالث: تعديل مباشر في الملف</h3>
            <p>إذا كان الملف لا يتم تحميله من المكان الصحيح:</p>
            
            <h4>ابحث عن الملف الصحيح:</h4>
            <ol>
                <li>افتح Developer Tools → Network</li>
                <li>أعد تحميل الصفحة</li>
                <li>ابحث عن <code>login.js</code> أو <code>login.min.js</code></li>
                <li>انقر على الملف لرؤية المسار الكامل</li>
                <li>عدل الملف في المسار الصحيح</li>
            </ol>
        </div>

        <div class="solution">
            <h3>📱 الحل الرابع: تفعيل WhatsApp Gateway</h3>
            <p>تأكد من تفعيل WhatsApp في إعدادات Digits:</p>
            <ol>
                <li>اذهب إلى WordPress Admin → Digits → Settings</li>
                <li>تبويب Gateways</li>
                <li>فعل WhatsApp Gateway</li>
                <li>أدخل API credentials الصحيحة</li>
                <li>احفظ الإعدادات</li>
            </ol>
        </div>

        <div class="warning">
            <h3>⚠️ استكشاف الأخطاء</h3>
            <p>إذا استمرت المشكلة:</p>
            <ul>
                <li>تأكد من عدم وجود أخطاء JavaScript في Console</li>
                <li>تحقق من أن jQuery محمل بشكل صحيح</li>
                <li>تأكد من أن Digits plugin مفعل ومحدث</li>
                <li>جرب تعطيل plugins أخرى مؤقتاً</li>
                <li>جرب تغيير الثيم مؤقتاً</li>
            </ul>
        </div>

        <div class="success">
            <h3>🎯 النتيجة المتوقعة</h3>
            <p>بعد تطبيق أي من الحلول أعلاه، يجب أن يحدث التالي:</p>
            <ol>
                <li>المستخدم يدخل رقم هاتف غير مسجل</li>
                <li>يضغط "Continue"</li>
                <li>تظهر رسالة "يرجى التسجيل أولاً..."</li>
                <li>بعد ثوانٍ يتم التحويل لنموذج التسجيل</li>
                <li>يتم ملء رقم الهاتف تلقائياً</li>
                <li>يتم استخدام WhatsApp للتحقق</li>
            </ol>
        </div>

        <div style="text-align: center; margin-top: 30px; padding: 20px; background: #e3f2fd; border-radius: 10px;">
            <h2 style="color: #1976d2; margin: 0;">📞 للمساعدة الإضافية</h2>
            <p style="margin: 10px 0 0 0;">إذا لم تعمل أي من الحلول، يرجى إرسال لقطة شاشة من Console و Network tabs</p>
        </div>
    </div>
</body>
</html>
