/**
 * Custom Login Redirect Script
 * يمكن إضافة هذا الكود مباشرة في WordPress
 * عبر إضافته في footer.php أو استخدام plugin لإضافة JavaScript مخصص
 */

jQuery(document).ready(function($) {
    console.log('Custom Login Redirect Script Loaded!');
    
    // Function to switch from login to registration form
    function switchToRegistrationForm(phoneNumber, countryCode) {
        console.log('Switching to registration form with:', phoneNumber, countryCode);
        
        var modal_container = $('.dig_lrf_box, .dig-box, .digits_modal_box').first();
        
        if (modal_container.length) {
            // Hide login form and show registration form
            modal_container.find('.digloginpage').hide();
            modal_container.find('.register').show();
            
            // Trigger the show register button if it exists
            var showRegisterBtn = modal_container.find('.show_register');
            if (showRegisterBtn.length) {
                showRegisterBtn.trigger('click');
            }
            
            // Pre-fill the phone number in registration form
            if (phoneNumber && countryCode) {
                var registerForm = modal_container.find('.register');
                
                // Fill main phone field
                registerForm.find('.dig-mobmail').val(phoneNumber);
                registerForm.find('.registercountrycode').val(countryCode);
                
                // Also fill secondary country code if exists
                registerForm.find('.registersecondcountrycode').val(countryCode);
                
                // Fill any other mobile fields
                registerForm.find('input[name*="phone"], input[name*="mobile"]').val(phoneNumber);
                registerForm.find('input[name*="countrycode"]').val(countryCode);
            }
            
            // Update modal height and focus
            setTimeout(function() {
                var registerMobileField = modal_container.find('.register .dig-mobmail');
                if (registerMobileField.length) {
                    registerMobileField.trigger('focus').trigger('keyup');
                }
                
                // Trigger window resize to adjust modal height
                $(window).trigger('resize');
            }, 100);
        }
    }
    
    // Override the AJAX success function for login verification
    var originalAjax = $.ajax;
    $.ajax = function(options) {
        // Check if this is the login verification request
        if (options.data && options.data.action === 'digits_check_mob' && options.data.login == 1) {
            console.log('Intercepting login verification request');
            
            var originalSuccess = options.success;
            options.success = function(result) {
                console.log('Login verification result:', result);
                
                var res = result;
                if (typeof result === 'object' && result.code !== undefined) {
                    res = result.code;
                } else if (typeof result === 'string') {
                    res = result.trim();
                }
                
                // Check if user doesn't exist (res == -11)
                if (res == -11) {
                    console.log('User does not exist, redirecting to registration');
                    
                    // Get phone number and country code
                    var phoneNumber = $('.dig_lrf_box .dig-mobmail').val();
                    var countryCode = $('.dig_lrf_box .logincountrycode').val();
                    
                    // Show message
                    if (typeof showDigNoticeMessage === 'function') {
                        showDigNoticeMessage('يرجى التسجيل أولاً. سيتم تحويلك إلى نموذج التسجيل خلال ثوانٍ...');
                    } else {
                        alert('يرجى التسجيل أولاً. سيتم تحويلك إلى نموذج التسجيل خلال ثوانٍ...');
                    }
                    
                    // Switch to registration form after delay
                    setTimeout(function() {
                        switchToRegistrationForm(phoneNumber, countryCode);
                        
                        // Show additional message
                        setTimeout(function() {
                            if (typeof showDigNoticeMessage === 'function') {
                                showDigNoticeMessage('يرجى إكمال بياناتك لإنشاء حساب جديد');
                            }
                        }, 500);
                    }, 2500);
                    
                    return; // Don't call original success function
                }
                
                // Call original success function for other cases
                if (originalSuccess) {
                    originalSuccess.call(this, result);
                }
            };
        }
        
        // Call original AJAX function
        return originalAjax.call(this, options);
    };
    
    // Also override the login button click to ensure WhatsApp is used
    $(document).off('click', '.dig_lrf_box .loginviasms');
    $(document).on('click', '.dig_lrf_box .loginviasms', function() {
        console.log('Custom login button clicked!');
        
        // Set WhatsApp as default if enabled
        if (typeof dig_log_obj !== 'undefined' && dig_log_obj.whatsapp_enabled) {
            window.useWhatsApp = 1;
            console.log('WhatsApp enabled for login');
        }
        
        // Continue with original login process
        var $this = $(this);
        var submit_form = $this.closest('form');
        var csrf = $('.dig_nounce').val();
        var countryCode = submit_form.find('.logincountrycode').val();
        var phoneNumber = submit_form.find('.dig-mobmail').val();
        
        if (phoneNumber == "" || countryCode == "") {
            if (typeof showDigErrorMessage === 'function') {
                showDigErrorMessage('رقم الهاتف غير صحيح');
            }
            return;
        }
        
        // Trigger the verification process
        if (typeof verifyMobileNoLogin === 'function') {
            verifyMobileNoLogin(countryCode, phoneNumber, csrf, 1);
        } else {
            console.log('verifyMobileNoLogin function not found, using AJAX directly');
            
            $.ajax({
                type: 'post',
                url: dig_log_obj.ajax_url,
                data: {
                    action: 'digits_check_mob',
                    countrycode: countryCode,
                    mobileNo: phoneNumber,
                    csrf: csrf,
                    login: 1,
                    digits: 1,
                    json: 1,
                    whatsapp: window.useWhatsApp || 0
                },
                success: function(result) {
                    console.log('Direct AJAX result:', result);
                    // The intercepted AJAX function will handle the response
                }
            });
        }
    });
    
    console.log('Custom Login Redirect Script Ready!');
});
